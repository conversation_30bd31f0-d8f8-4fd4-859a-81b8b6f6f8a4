import 'package:fit_check_app/utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.primaryMaroon,
      scaffoldBackgroundColor: AppColors.backgroundOffWhite,
      textTheme: GoogleFonts.robotoTextTheme().copyWith(
        headlineLarge: const TextStyle(fontSize: 28, fontWeight: FontWeight.bold, color: AppColors.darkGray),
        headlineMedium: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: AppColors.darkGray),
        headlineSmall: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: AppColors.darkGray),
        bodyLarge: const TextStyle(fontSize: 17, color: AppColors.darkGray),
        bodyMedium: const TextStyle(fontSize: 15, color: AppColors.darkGray),
        bodySmall: const TextStyle(fontSize: 13, color: AppColors.neutralGray),
        labelLarge: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: AppColors.primaryWhite),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryMaroon,
          foregroundColor: AppColors.primaryWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.neutralGray.withOpacity(0.5), width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primaryMaroon, width: 2),
        ),
        labelStyle: const TextStyle(color: AppColors.neutralGray),
      ),
    );
  }
}
